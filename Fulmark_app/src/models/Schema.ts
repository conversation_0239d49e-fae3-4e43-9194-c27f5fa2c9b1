import {
  integer,
  pgTable,
  serial,
  timestamp,
  text,
  varchar,
  boolean,
  numeric,
  json,
  pgEnum,
  type AnyPgColumn
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// 🌟 HVAC COSMIC CRM DATABASE SCHEMA - PEŁNA MOC WIATRU! ⚡
// Complete HVAC management system with AI-powered features

// ========== ENUMS ==========
export const customerFlowStateEnum = pgEnum('customer_flow_state', [
  'initial_contact',
  'quote_requested',
  'quote_sent',
  'quote_approved',
  'service_scheduled',
  'service_in_progress',
  'service_completed',
  'invoice_sent',
  'payment_received',
  'follow_up_scheduled',
  'maintenance_program'
]);

export const serviceTicketStatusEnum = pgEnum('service_ticket_status', [
  'new',
  'assigned',
  'in_progress',
  'waiting_parts',
  'completed',
  'cancelled',
  'on_hold'
]);

export const serviceTicketPriorityEnum = pgEnum('service_ticket_priority', [
  'low',
  'normal',
  'high',
  'urgent',
  'emergency'
]);

export const technicianStatusEnum = pgEnum('technician_status', [
  'available',
  'busy',
  'on_route',
  'offline',
  'break'
]);

export const communicationTypeEnum = pgEnum('communication_type', [
  'email',
  'phone',
  'sms',
  'in_person',
  'chat'
]);

export const communicationDirectionEnum = pgEnum('communication_direction', [
  'inbound',
  'outbound'
]);

export const equipmentStatusEnum = pgEnum('equipment_status', [
  'active',
  'maintenance_required',
  'out_of_service',
  'retired'
]);

export const invoiceStatusEnum = pgEnum('invoice_status', [
  'draft',
  'sent',
  'paid',
  'overdue',
  'cancelled'
]);

// ========== CORE TABLES ==========

// Companies (Multi-tenant support)
export const companies = pgTable('companies', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zipCode: varchar('zip_code', { length: 20 }),
  country: varchar('country', { length: 100 }).default('Poland'),
  website: varchar('website', { length: 255 }),
  logo: text('logo'), // URL to logo
  settings: json('settings'), // Company-specific settings
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Customers
export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  alternatePhone: varchar('alternate_phone', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zipCode: varchar('zip_code', { length: 20 }),
  country: varchar('country', { length: 100 }).default('Poland'),

  // Customer flow management
  flowState: customerFlowStateEnum('flow_state').default('initial_contact'),
  preferredContact: communicationTypeEnum('preferred_contact').default('email'),

  // Business intelligence
  customerValue: numeric('customer_value', { precision: 10, scale: 2 }).default('0'),
  lastServiceDate: timestamp('last_service_date', { mode: 'date' }),
  nextMaintenanceDate: timestamp('next_maintenance_date', { mode: 'date' }),

  // AI insights
  healthScore: integer('health_score').default(100), // 0-100
  churnRisk: integer('churn_risk').default(0), // 0-100
  sentimentScore: numeric('sentiment_score', { precision: 3, scale: 2 }).default('0'), // -1 to 1

  // Metadata
  source: varchar('source', { length: 100 }), // website, referral, etc.
  notes: text('notes'),
  tags: json('tags'), // Array of tags
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Buildings/Properties
export const buildings = pgTable('buildings', {
  id: serial('id').primaryKey(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  name: varchar('name', { length: 255 }),
  address: text('address').notNull(),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zipCode: varchar('zip_code', { length: 20 }),

  // Building details
  buildingType: varchar('building_type', { length: 100 }), // residential, commercial, industrial
  squareFootage: integer('square_footage'),
  floors: integer('floors').default(1),
  yearBuilt: integer('year_built'),

  // HVAC specific
  heatingType: varchar('heating_type', { length: 100 }),
  coolingType: varchar('cooling_type', { length: 100 }),

  // Location data
  latitude: numeric('latitude', { precision: 10, scale: 8 }),
  longitude: numeric('longitude', { precision: 11, scale: 8 }),

  notes: text('notes'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Technicians
export const technicians = pgTable('technicians', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  employeeId: varchar('employee_id', { length: 50 }),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),

  // Work details
  status: technicianStatusEnum('status').default('available'),
  specializations: json('specializations'), // Array of specializations
  certifications: json('certifications'), // Array of certifications
  hourlyRate: numeric('hourly_rate', { precision: 8, scale: 2 }),

  // Location tracking
  currentLatitude: numeric('current_latitude', { precision: 10, scale: 8 }),
  currentLongitude: numeric('current_longitude', { precision: 11, scale: 8 }),
  lastLocationUpdate: timestamp('last_location_update', { mode: 'date' }),

  // Performance metrics
  averageRating: numeric('average_rating', { precision: 3, scale: 2 }).default('5.0'),
  completedJobs: integer('completed_jobs').default(0),

  // Availability
  workingHours: json('working_hours'), // Schedule data
  isActive: boolean('is_active').default(true),
  hireDate: timestamp('hire_date', { mode: 'date' }),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Service Tickets
export const serviceTickets = pgTable('service_tickets', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  buildingId: integer('building_id').references(() => buildings.id),
  assignedTechnicianId: integer('assigned_technician_id').references(() => technicians.id),

  // Ticket details
  ticketNumber: varchar('ticket_number', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  status: serviceTicketStatusEnum('status').default('new'),
  priority: serviceTicketPriorityEnum('priority').default('normal'),

  // Service details
  serviceType: varchar('service_type', { length: 100 }), // installation, repair, maintenance
  equipmentType: varchar('equipment_type', { length: 100 }),
  problemCategory: varchar('problem_category', { length: 100 }),

  // Scheduling
  scheduledDate: timestamp('scheduled_date', { mode: 'date' }),
  estimatedDuration: integer('estimated_duration'), // in minutes
  actualStartTime: timestamp('actual_start_time', { mode: 'date' }),
  actualEndTime: timestamp('actual_end_time', { mode: 'date' }),

  // Financial
  estimatedCost: numeric('estimated_cost', { precision: 10, scale: 2 }),
  actualCost: numeric('actual_cost', { precision: 10, scale: 2 }),
  laborHours: numeric('labor_hours', { precision: 5, scale: 2 }),

  // AI insights
  urgencyScore: integer('urgency_score').default(50), // 0-100
  complexityScore: integer('complexity_score').default(50), // 0-100

  // Work details
  workPerformed: text('work_performed'),
  partsUsed: json('parts_used'), // Array of parts
  notes: text('notes'),
  internalNotes: text('internal_notes'),

  // Customer satisfaction
  customerRating: integer('customer_rating'), // 1-5
  customerFeedback: text('customer_feedback'),

  // Photos and attachments
  beforePhotos: json('before_photos'), // Array of photo URLs
  afterPhotos: json('after_photos'), // Array of photo URLs
  attachments: json('attachments'), // Array of attachment URLs

  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Equipment Registry
export const equipment = pgTable('equipment', {
  id: serial('id').primaryKey(),
  buildingId: integer('building_id').references(() => buildings.id).notNull(),

  // Equipment details
  name: varchar('name', { length: 255 }).notNull(),
  manufacturer: varchar('manufacturer', { length: 100 }),
  model: varchar('model', { length: 100 }),
  serialNumber: varchar('serial_number', { length: 100 }),
  equipmentType: varchar('equipment_type', { length: 100 }), // HVAC, furnace, AC, etc.

  // Installation details
  installationDate: timestamp('installation_date', { mode: 'date' }),
  warrantyExpiration: timestamp('warranty_expiration', { mode: 'date' }),
  lastMaintenanceDate: timestamp('last_maintenance_date', { mode: 'date' }),
  nextMaintenanceDate: timestamp('next_maintenance_date', { mode: 'date' }),

  // Status and health
  status: equipmentStatusEnum('status').default('active'),
  healthScore: integer('health_score').default(100), // 0-100

  // Technical specifications
  specifications: json('specifications'),
  manualUrl: text('manual_url'),

  // Maintenance tracking
  maintenanceHistory: json('maintenance_history'),

  notes: text('notes'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Communications
export const communications = pgTable('communications', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id),
  serviceTicketId: integer('service_ticket_id').references(() => serviceTickets.id),

  // Communication details
  type: communicationTypeEnum('type').notNull(),
  direction: communicationDirectionEnum('direction').notNull(),
  subject: varchar('subject', { length: 500 }),
  content: text('content'),

  // Contact information
  fromEmail: varchar('from_email', { length: 255 }),
  toEmail: varchar('to_email', { length: 255 }),
  fromPhone: varchar('from_phone', { length: 50 }),
  toPhone: varchar('to_phone', { length: 50 }),

  // AI analysis
  sentimentScore: numeric('sentiment_score', { precision: 3, scale: 2 }), // -1 to 1
  category: varchar('category', { length: 100 }), // inquiry, complaint, emergency, etc.
  priority: serviceTicketPriorityEnum('priority').default('normal'),
  intent: varchar('intent', { length: 100 }), // AI-detected intent

  // Processing flags
  autoResponseSent: boolean('auto_response_sent').default(false),
  requiresHumanAttention: boolean('requires_human_attention').default(false),
  isProcessed: boolean('is_processed').default(false),

  // Attachments and transcriptions
  attachments: json('attachments'), // Array of attachment URLs
  transcriptionId: integer('transcription_id'), // Reference to transcriptions table

  // Threading
  threadId: varchar('thread_id', { length: 100 }),
  parentId: integer('parent_id').references((): AnyPgColumn => communications.id),

  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Invoices
export const invoices = pgTable('invoices', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id).notNull(),
  serviceTicketId: integer('service_ticket_id').references(() => serviceTickets.id),

  // Invoice details
  invoiceNumber: varchar('invoice_number', { length: 50 }).notNull(),
  status: invoiceStatusEnum('status').default('draft'),

  // Financial details
  subtotal: numeric('subtotal', { precision: 10, scale: 2 }).notNull(),
  taxAmount: numeric('tax_amount', { precision: 10, scale: 2 }).default('0'),
  discountAmount: numeric('discount_amount', { precision: 10, scale: 2 }).default('0'),
  totalAmount: numeric('total_amount', { precision: 10, scale: 2 }).notNull(),

  // Line items
  lineItems: json('line_items'), // Array of invoice line items

  // Dates
  issueDate: timestamp('issue_date', { mode: 'date' }).defaultNow().notNull(),
  dueDate: timestamp('due_date', { mode: 'date' }),
  paidDate: timestamp('paid_date', { mode: 'date' }),

  // Payment details
  paymentMethod: varchar('payment_method', { length: 50 }),
  paymentReference: varchar('payment_reference', { length: 100 }),

  // Additional details
  terms: text('terms'),
  notes: text('notes'),

  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Parts Inventory
export const partsInventory = pgTable('parts_inventory', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),

  // Part details
  partNumber: varchar('part_number', { length: 100 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  manufacturer: varchar('manufacturer', { length: 100 }),
  category: varchar('category', { length: 100 }),

  // Inventory tracking
  quantityInStock: integer('quantity_in_stock').default(0),
  minimumStock: integer('minimum_stock').default(0),
  maximumStock: integer('maximum_stock'),
  reorderPoint: integer('reorder_point').default(0),

  // Pricing
  cost: numeric('cost', { precision: 10, scale: 2 }),
  price: numeric('price', { precision: 10, scale: 2 }),

  // Supplier information
  supplierName: varchar('supplier_name', { length: 255 }),
  supplierPartNumber: varchar('supplier_part_number', { length: 100 }),

  // Physical details
  location: varchar('location', { length: 100 }), // Warehouse location
  weight: numeric('weight', { precision: 8, scale: 3 }),
  dimensions: varchar('dimensions', { length: 100 }),

  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Transcriptions (Voice/Audio processing)
export const transcriptions = pgTable('transcriptions', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').references(() => companies.id).notNull(),
  customerId: integer('customer_id').references(() => customers.id),
  serviceTicketId: integer('service_ticket_id').references(() => serviceTickets.id),

  // Audio details
  audioUrl: text('audio_url'), // URL to audio file
  transcript: text('transcript').notNull(),
  context: varchar('context', { length: 50 }), // customer_call, technician_note, voicemail

  // Processing details
  confidence: numeric('confidence', { precision: 5, scale: 4 }), // 0-1
  duration: integer('duration'), // in seconds
  language: varchar('language', { length: 10 }).default('pl'),

  // AI analysis
  analysis: json('analysis'), // AI-extracted insights
  intent: varchar('intent', { length: 100 }),
  sentimentScore: numeric('sentiment_score', { precision: 3, scale: 2 }),
  urgencyLevel: serviceTicketPriorityEnum('urgency_level').default('normal'),

  // Processing flags
  isProcessed: boolean('is_processed').default(false),
  followUpRequired: boolean('follow_up_required').default(false),

  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
});

// Keep the original counter table for compatibility
export const counterSchema = pgTable('counter', {
  id: serial('id').primaryKey(),
  count: integer('count').default(0),
  updatedAt: timestamp('updated_at', { mode: 'date' })
    .defaultNow()
    .$onUpdate(() => new Date())
    .notNull(),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
});

// ========== RELATIONS ==========

export const companiesRelations = relations(companies, ({ many }) => ({
  customers: many(customers),
  technicians: many(technicians),
  serviceTickets: many(serviceTickets),
  communications: many(communications),
  invoices: many(invoices),
  partsInventory: many(partsInventory),
  transcriptions: many(transcriptions),
}));

export const customersRelations = relations(customers, ({ one, many }) => ({
  company: one(companies, { fields: [customers.companyId], references: [companies.id] }),
  buildings: many(buildings),
  serviceTickets: many(serviceTickets),
  communications: many(communications),
  invoices: many(invoices),
  transcriptions: many(transcriptions),
}));

export const buildingsRelations = relations(buildings, ({ one, many }) => ({
  customer: one(customers, { fields: [buildings.customerId], references: [customers.id] }),
  serviceTickets: many(serviceTickets),
  equipment: many(equipment),
}));

export const techniciansRelations = relations(technicians, ({ one, many }) => ({
  company: one(companies, { fields: [technicians.companyId], references: [companies.id] }),
  serviceTickets: many(serviceTickets),
}));

export const serviceTicketsRelations = relations(serviceTickets, ({ one, many }) => ({
  company: one(companies, { fields: [serviceTickets.companyId], references: [companies.id] }),
  customer: one(customers, { fields: [serviceTickets.customerId], references: [customers.id] }),
  building: one(buildings, { fields: [serviceTickets.buildingId], references: [buildings.id] }),
  assignedTechnician: one(technicians, { fields: [serviceTickets.assignedTechnicianId], references: [technicians.id] }),
  communications: many(communications),
  invoices: many(invoices),
  transcriptions: many(transcriptions),
}));

export const equipmentRelations = relations(equipment, ({ one }) => ({
  building: one(buildings, { fields: [equipment.buildingId], references: [buildings.id] }),
}));

export const communicationsRelations = relations(communications, ({ one, many }) => ({
  company: one(companies, { fields: [communications.companyId], references: [companies.id] }),
  customer: one(customers, { fields: [communications.customerId], references: [customers.id] }),
  serviceTicket: one(serviceTickets, { fields: [communications.serviceTicketId], references: [serviceTickets.id] }),
  parent: one(communications, { fields: [communications.parentId], references: [communications.id] }),
  replies: many(communications),
}));

export const invoicesRelations = relations(invoices, ({ one }) => ({
  company: one(companies, { fields: [invoices.companyId], references: [companies.id] }),
  customer: one(customers, { fields: [invoices.customerId], references: [customers.id] }),
  serviceTicket: one(serviceTickets, { fields: [invoices.serviceTicketId], references: [serviceTickets.id] }),
}));

export const partsInventoryRelations = relations(partsInventory, ({ one }) => ({
  company: one(companies, { fields: [partsInventory.companyId], references: [companies.id] }),
}));

export const transcriptionsRelations = relations(transcriptions, ({ one }) => ({
  company: one(companies, { fields: [transcriptions.companyId], references: [companies.id] }),
  customer: one(customers, { fields: [transcriptions.customerId], references: [customers.id] }),
  serviceTicket: one(serviceTickets, { fields: [transcriptions.serviceTicketId], references: [serviceTickets.id] }),
}));
