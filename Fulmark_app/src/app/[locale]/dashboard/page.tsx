import { getTranslations } from 'next-intl/server';
import HVACDashboard from '@/components/hvac/dashboard/HVACDashboard';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const DashboardPage = () => {
  return <HVACDashboard />;
};

export default DashboardPage;