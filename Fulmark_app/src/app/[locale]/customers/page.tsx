import { getTranslations } from 'next-intl/server';
import CustomerList from '@/components/hvac/customers/CustomerList';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Customers',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

const CustomersPage = () => {
  return <CustomerList />;
};

export default CustomersPage;