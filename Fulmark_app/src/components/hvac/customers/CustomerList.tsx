'use client';

// 👥 Customer List Component - PEŁNA MOC WIATRU! ⚡

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Filter,
  Plus,
  Mail,
  Phone,
  MapPin,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Users
} from 'lucide-react';

interface Customer {
  id: number;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  city?: string;
  flowState: string;
  healthScore: number;
  churnRisk: number;
  customerValue: number;
  lastServiceDate?: string;
  createdAt: string;
}

const CustomerList: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterState, setFilterState] = useState('all');

  // Mock data for demonstration
  useEffect(() => {
    setTimeout(() => {
      setCustomers([
        {
          id: 1,
          firstName: 'Jan',
          lastName: '<PERSON><PERSON><PERSON>',
          email: 'jan.kowa<PERSON><PERSON>@email.com',
          phone: '+48 123 456 789',
          city: 'Warszawa',
          flowState: 'maintenance_program',
          healthScore: 95,
          churnRisk: 15,
          customerValue: 15000,
          lastServiceDate: '2024-01-15',
          createdAt: '2023-06-15',
        },
        {
          id: 2,
          firstName: 'Anna',
          lastName: 'Nowak',
          email: '<EMAIL>',
          phone: '+48 987 654 321',
          city: 'Kraków',
          flowState: 'service_completed',
          healthScore: 87,
          churnRisk: 25,
          customerValue: 8500,
          lastServiceDate: '2024-01-10',
          createdAt: '2023-08-20',
        },
        {
          id: 3,
          firstName: 'Piotr',
          lastName: 'Wiśniewski',
          email: '<EMAIL>',
          phone: '+48 555 123 456',
          city: 'Gdańsk',
          flowState: 'quote_sent',
          healthScore: 72,
          churnRisk: 45,
          customerValue: 3200,
          createdAt: '2024-01-05',
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getFlowStateColor = (state: string) => {
    const colors = {
      'initial_contact': 'bg-gray-100 text-gray-800',
      'quote_requested': 'bg-blue-100 text-blue-800',
      'quote_sent': 'bg-yellow-100 text-yellow-800',
      'quote_approved': 'bg-green-100 text-green-800',
      'service_scheduled': 'bg-purple-100 text-purple-800',
      'service_in_progress': 'bg-orange-100 text-orange-800',
      'service_completed': 'bg-green-100 text-green-800',
      'invoice_sent': 'bg-blue-100 text-blue-800',
      'payment_received': 'bg-green-100 text-green-800',
      'follow_up_scheduled': 'bg-purple-100 text-purple-800',
      'maintenance_program': 'bg-emerald-100 text-emerald-800',
    };
    return colors[state as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getFlowStateLabel = (state: string) => {
    const labels = {
      'initial_contact': 'Pierwszy Kontakt',
      'quote_requested': 'Prośba o Wycenę',
      'quote_sent': 'Wycena Wysłana',
      'quote_approved': 'Wycena Zatwierdzona',
      'service_scheduled': 'Serwis Zaplanowany',
      'service_in_progress': 'Serwis w Trakcie',
      'service_completed': 'Serwis Zakończony',
      'invoice_sent': 'Faktura Wysłana',
      'payment_received': 'Płatność Otrzymana',
      'follow_up_scheduled': 'Follow-up Zaplanowany',
      'maintenance_program': 'Program Konserwacji',
    };
    return labels[state as keyof typeof labels] || state;
  };

  const getHealthScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (score >= 60) return <TrendingUp className="w-4 h-4 text-yellow-500" />;
    return <AlertTriangle className="w-4 h-4 text-red-500" />;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    }).format(amount);
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = 
      customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.includes(searchTerm);
    
    const matchesFilter = filterState === 'all' || customer.flowState === filterState;
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Zarządzanie Klientami</h1>
            <p className="text-gray-600 mt-1">Kompletne zarządzanie bazą klientów z AI insights</p>
          </div>
          <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4" />
            <span>Nowy Klient</span>
          </button>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Szukaj klientów..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={filterState}
              onChange={(e) => setFilterState(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
            >
              <option value="all">Wszystkie stany</option>
              <option value="initial_contact">Pierwszy Kontakt</option>
              <option value="quote_sent">Wycena Wysłana</option>
              <option value="service_completed">Serwis Zakończony</option>
              <option value="maintenance_program">Program Konserwacji</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Customer List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg shadow-sm overflow-hidden"
      >
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Klient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Kontakt
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Health Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Wartość
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ostatni Serwis
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCustomers.map((customer) => (
                <motion.tr
                  key={customer.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50 cursor-pointer"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {customer.firstName} {customer.lastName}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        {customer.city}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      {customer.email && (
                        <div className="text-sm text-gray-900 flex items-center">
                          <Mail className="w-3 h-3 mr-2 text-gray-400" />
                          {customer.email}
                        </div>
                      )}
                      {customer.phone && (
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="w-3 h-3 mr-2 text-gray-400" />
                          {customer.phone}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getFlowStateColor(customer.flowState)}`}>
                      {getFlowStateLabel(customer.flowState)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getHealthScoreIcon(customer.healthScore)}
                      <span className="ml-2 text-sm font-medium text-gray-900">
                        {customer.healthScore}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(customer.customerValue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.lastServiceDate 
                      ? new Date(customer.lastServiceDate).toLocaleDateString('pl-PL')
                      : 'Brak'
                    }
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCustomers.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Brak klientów</p>
              <p className="text-sm">Nie znaleziono klientów spełniających kryteria wyszukiwania.</p>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default CustomerList;