'use client';

// 🌟 HVAC Dashboard - PEŁNA MOC WIATRU! ⚡
// Complete dashboard with cosmic-level UX and AI insights

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Wrench, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Clock,
  Star,
  Activity
} from 'lucide-react';

interface DashboardStats {
  customers: {
    total: number;
    newThisMonth: number;
    active: number;
    averageHealthScore: number;
  };
  serviceTickets: {
    total: number;
    open: number;
    inProgress: number;
    completed: number;
    averageRating: number;
  };
  revenue: {
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  technicians: {
    total: number;
    available: number;
    busy: number;
    averageRating: number;
  };
}

interface RecentActivity {
  id: number;
  type: 'ticket' | 'customer' | 'communication';
  title: string;
  description: string;
  timestamp: Date;
  priority?: 'low' | 'normal' | 'high' | 'urgent' | 'emergency';
}

const HVACDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setStats({
        customers: {
          total: 1247,
          newThisMonth: 89,
          active: 892,
          averageHealthScore: 87,
        },
        serviceTickets: {
          total: 2156,
          open: 23,
          inProgress: 15,
          completed: 2118,
          averageRating: 4.7,
        },
        revenue: {
          thisMonth: 125000,
          lastMonth: 98000,
          growth: 27.6,
        },
        technicians: {
          total: 12,
          available: 8,
          busy: 4,
          averageRating: 4.8,
        },
      });

      setRecentActivity([
        {
          id: 1,
          type: 'ticket',
          title: 'Emergency AC Repair',
          description: 'High priority ticket created for Kowalski residence',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          priority: 'emergency',
        },
        {
          id: 2,
          type: 'customer',
          title: 'New Customer Registration',
          description: 'Anna Nowak registered via website contact form',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
        },
        {
          id: 3,
          type: 'communication',
          title: 'Email Response Sent',
          description: 'Automated quote <NAME_EMAIL>',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        },
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    }).format(amount);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} min temu`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} godz. temu`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} dni temu`;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'emergency': return 'text-red-600 bg-red-100';
      case 'urgent': return 'text-orange-600 bg-orange-100';
      case 'high': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Panel Główny HVAC</h1>
            <p className="text-gray-600 mt-1">Zarządzanie firmą klimatyzacyjną z pełną mocą wiatru! ⚡</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Dzisiaj</p>
              <p className="text-lg font-semibold text-gray-900">
                {new Date().toLocaleDateString('pl-PL', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Customers Stats */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Klienci</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.customers.total}</p>
              <p className="text-sm text-green-600">+{stats?.customers.newThisMonth} w tym miesiącu</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Aktywni</span>
              <span className="font-medium">{stats?.customers.active}</span>
            </div>
            <div className="flex justify-between text-sm mt-1">
              <span className="text-gray-600">Średni Health Score</span>
              <span className="font-medium text-green-600">{stats?.customers.averageHealthScore}%</span>
            </div>
          </div>
        </motion.div>

        {/* Service Tickets Stats */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Zlecenia Serwisowe</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.serviceTickets.total}</p>
              <p className="text-sm text-orange-600">{stats?.serviceTickets.open} otwartych</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Wrench className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">W trakcie</span>
              <span className="font-medium">{stats?.serviceTickets.inProgress}</span>
            </div>
            <div className="flex justify-between text-sm mt-1">
              <span className="text-gray-600">Średnia ocena</span>
              <span className="font-medium text-yellow-600">
                {stats?.serviceTickets.averageRating} ⭐
              </span>
            </div>
          </div>
        </motion.div>

        {/* Revenue Stats */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Przychody</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(stats?.revenue.thisMonth || 0)}
              </p>
              <p className="text-sm text-green-600">+{stats?.revenue.growth}% vs poprzedni miesiąc</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Poprzedni miesiąc</span>
              <span className="font-medium">{formatCurrency(stats?.revenue.lastMonth || 0)}</span>
            </div>
            <div className="flex justify-between text-sm mt-1">
              <span className="text-gray-600">Wzrost</span>
              <span className="font-medium text-green-600">
                +{formatCurrency((stats?.revenue.thisMonth || 0) - (stats?.revenue.lastMonth || 0))}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Technicians Stats */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Technicy</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.technicians.total}</p>
              <p className="text-sm text-blue-600">{stats?.technicians.available} dostępnych</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Activity className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Zajęci</span>
              <span className="font-medium">{stats?.technicians.busy}</span>
            </div>
            <div className="flex justify-between text-sm mt-1">
              <span className="text-gray-600">Średnia ocena</span>
              <span className="font-medium text-yellow-600">
                {stats?.technicians.averageRating} ⭐
              </span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Ostatnia Aktywność</h2>
        <div className="space-y-4">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className={`p-2 rounded-full ${getPriorityColor(activity.priority)}`}>
                {activity.type === 'ticket' && <Wrench className="w-4 h-4" />}
                {activity.type === 'customer' && <Users className="w-4 h-4" />}
                {activity.type === 'communication' && <Mail className="w-4 h-4" />}
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{activity.title}</h3>
                <p className="text-sm text-gray-600">{activity.description}</p>
                <p className="text-xs text-gray-500 mt-1">{formatTimeAgo(activity.timestamp)}</p>
              </div>
              {activity.priority && (
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(activity.priority)}`}>
                  {activity.priority.toUpperCase()}
                </span>
              )}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-lg shadow-sm p-6"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Szybkie Akcje</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <Users className="w-8 h-8 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-blue-900">Nowy Klient</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
            <Wrench className="w-8 h-8 text-orange-600 mb-2" />
            <span className="text-sm font-medium text-orange-900">Nowe Zlecenie</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <Calendar className="w-8 h-8 text-green-600 mb-2" />
            <span className="text-sm font-medium text-green-900">Harmonogram</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <TrendingUp className="w-8 h-8 text-purple-600 mb-2" />
            <span className="text-sm font-medium text-purple-900">Raporty</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default HVACDashboard;