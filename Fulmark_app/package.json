{"name": "next-js-boilerplate", "version": "3.68.0", "author": "Ixartz (https://github.com/ixartz)", "engines": {"node": ">=20"}, "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev --turbopack", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run --browser.headless", "test:e2e": "playwright test", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook"}, "dependencies": {"@arcjet/next": "^1.0.0-beta.8", "@clerk/localizations": "^3.16.3", "@clerk/nextjs": "^6.20.2", "@electric-sql/pglite": "^0.3.2", "@hookform/resolvers": "^5.0.1", "@logtail/pino": "^0.5.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/render": "^1.1.2", "@sentry/nextjs": "^9.24.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@t3-oss/env-nextjs": "^0.13.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "^15.3.3", "next-intl": "^4.1.0", "openai": "^5.1.0", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "posthog-js": "^1.249.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "resend": "^4.5.1", "tailwind-merge": "^3.3.0", "twilio": "^5.7.0", "zod": "^3.25.46"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/cz-commitlint": "^19.8.1", "@eslint-react/eslint-plugin": "^1.50.0", "@faker-js/faker": "^9.8.0", "@next/bundle-analyzer": "^15.3.3", "@next/eslint-plugin-next": "^15.3.3", "@percy/cli": "1.30.11", "@percy/playwright": "^1.0.8", "@playwright/test": "^1.52.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@spotlightjs/spotlight": "^2.13.3", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/test-runner": "^0.22.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.15.29", "@types/pg": "^8.15.4", "@types/react": "^19.1.6", "@vitejs/plugin-react": "^4.5.0", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "@vitest/expect": "^3.1.4", "checkly": "^5.5.0", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.28.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "http-server": "^14.1.1", "lefthook": "^1.11.13", "npm-run-all": "^4.1.5", "postcss": "^8.5.4", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.5", "start-server-and-test": "^2.0.12", "storybook": "^8.6.14", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4", "vitest-browser-react": "^0.2.0"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}