# FIXME: Configure environment variables for production

# If you need to build a SaaS application with Stripe subscription payment with checkout page, customer portal, webhook, etc.
# You can check out the Next.js Boilerplate SaaS: https://nextjs-boilerplate.com/pro-saas-starter-kit

# Hosting
# Replace by your domain name
# NEXT_PUBLIC_APP_URL=https://example.com

# Sentry DSN
NEXT_PUBLIC_SENTRY_DSN=

######## [BEGIN] SENSITIVE DATA ######## For security reason, don't update the following variables (secret key) directly in this file.
######## Please create a new file named `.env.production.local`, all environment files ending with `.local` won't be tracked by Git.
######## After creating the file, you can add the following variables.
# Arcjet security
# Get your key from https://launch.arcjet.com/Q6eLbRE
# ARCJET_KEY=

# Database
# Using an incorrect DATABASE_URL value, Next.js build will timeout and you will get the following error: "because it took more than 60 seconds"
# DATABASE_URL=postgresql://postgres@localhost:5432/postgres

# Error monitoring
# SENTRY_AUTH_TOKEN=

# Logging ingestion
# LOGTAIL_SOURCE_TOKEN=
######## [END] SENSITIVE DATA
