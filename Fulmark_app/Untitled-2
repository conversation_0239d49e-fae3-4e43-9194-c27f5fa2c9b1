// Intelligent Search Orchestration
class HVACIntelligenceEngine {
  async searchEquipmentDocs(query: string, equipmentModel: string) {
    // Use Brave Search for general documentation
    const generalResults = await braveSearch.query(
      `${equipmentModel} ${query} manual specifications`
    );
    
    // Use Tavily MCP for AI-enhanced analysis
    const aiAnalysis = await tavilyMCP.analyze({
      query: `HVAC troubleshooting: ${query}`,
      context: equipmentModel,
      extractDepth: 'advanced'
    });
    
    return this.synthesizeResults(generalResults, aiAnalysis);
  }
}