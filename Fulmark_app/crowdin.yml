#
# Your Crowdin credentials
#
# No need modify CROWDIN_PROJECT_ID and CROWDIN_PERSONAL_TOKEN, you can set them in GitHub Actions secrets
project_id_env: CROWDIN_PROJECT_ID
api_token_env: CROWDIN_PERSONAL_TOKEN
base_path: .
base_url: 'https://api.crowdin.com' # https://{organization-name}.crowdin.com for Crowdin Enterprise

#
# Choose file structure in Crowdin
# e.g. true or false
#
preserve_hierarchy: true

#
# Files configuration
#
files:
  - source: /src/locales/en.json

    #
    # Where translations will be placed
    # e.g. "/resources/%two_letters_code%/%original_file_name%"
    #
    translation: '/src/locales/%two_letters_code%.json'

    #
    # File type
    # e.g. "json"
    #
    type: json
