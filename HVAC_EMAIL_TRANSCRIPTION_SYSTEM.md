# 📧 HVAC EMAIL & TRANSCRIPTION SYSTEM - PEŁNA MOC WIATRU

## 🚀 **INTELLIGENT EMAIL PROCESSING & VOICE TRANSCRIPTION WITH COSMIC POWER**

### 🌟 **Email Processing Pipeline**

```typescript
// Email Processing Architecture
interface EmailProcessor {
  inbound: InboundEmailHandler;
  outbound: OutboundEmailHandler;
  automation: EmailAutomationEngine;
  templates: EmailTemplateManager;
  analytics: EmailAnalytics;
}

interface InboundEmail {
  id: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  attachments: EmailAttachment[];
  timestamp: Date;
  thread_id?: string;
  customer_id?: string;
  service_ticket_id?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category: 'inquiry' | 'complaint' | 'emergency' | 'quote_request' | 'payment';
  sentiment: number; // -1 to 1
  auto_response_sent: boolean;
  requires_human_attention: boolean;
}

interface EmailAttachment {
  id: string;
  filename: string;
  content_type: string;
  size: number;
  url: string;
  extracted_text?: string;
  is_invoice?: boolean;
  is_photo?: boolean;
}
```

### 📨 **Inbound Email Processing System**

```typescript
// src/services/emailProcessor.service.ts
import { supabase } from './supabase';
import { OpenAI } from 'openai';
import { EmailAnalyzer } from './emailAnalyzer.service';
import { CustomerService } from './customer.service';

export class EmailProcessorService {
  private openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  private emailAnalyzer = new EmailAnalyzer();
  private customerService = new CustomerService();

  async processInboundEmail(emailData: any) {
    try {
      // 1. Parse and clean email
      const cleanedEmail = await this.parseEmail(emailData);
      
      // 2. Identify customer
      const customer = await this.identifyCustomer(cleanedEmail);
      
      // 3. Analyze email content
      const analysis = await this.analyzeEmailContent(cleanedEmail);
      
      // 4. Categorize and prioritize
      const categorization = await this.categorizeEmail(cleanedEmail, analysis);
      
      // 5. Process attachments
      const processedAttachments = await this.processAttachments(cleanedEmail.attachments);
      
      // 6. Store in database
      const storedEmail = await this.storeEmail({
        ...cleanedEmail,
        customer_id: customer?.id,
        ...categorization,
        attachments: processedAttachments
      });
      
      // 7. Trigger automated responses
      await this.triggerAutomatedResponse(storedEmail, customer, analysis);
      
      // 8. Create or update service ticket if needed
      if (categorization.requires_ticket) {
        await this.createServiceTicket(storedEmail, customer, analysis);
      }
      
      // 9. Notify team if human attention required
      if (categorization.requires_human_attention) {
        await this.notifyTeam(storedEmail, categorization);
      }

      return storedEmail;
    } catch (error) {
      console.error('Email processing error:', error);
      await this.handleEmailProcessingError(emailData, error);
    }
  }

  private async analyzeEmailContent(email: any) {
    const prompt = `
      Analyze this HVAC customer email and extract key information:
      
      Subject: ${email.subject}
      Body: ${email.body}
      
      Please provide:
      1. Intent (inquiry, complaint, emergency, quote_request, scheduling, payment)
      2. Urgency level (low, normal, high, urgent)
      3. Sentiment score (-1 to 1)
      4. Key details (equipment type, problem description, preferred dates)
      5. Customer emotion (frustrated, satisfied, neutral, angry)
      6. Requires immediate attention (yes/no)
      7. Suggested response type (automated, human, technical)
      
      Return as JSON.
    `;

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    });

    return JSON.parse(response.choices[0].message.content);
  }

  private async identifyCustomer(email: any): Promise<Customer | null> {
    // Try to find customer by email
    let customer = await this.customerService.findByEmail(email.from);
    
    if (!customer) {
      // Try to find by phone number in signature
      const phoneNumbers = this.extractPhoneNumbers(email.body);
      for (const phone of phoneNumbers) {
        customer = await this.customerService.findByPhone(phone);
        if (customer) break;
      }
    }
    
    if (!customer) {
      // Create new lead
      customer = await this.customerService.createLead({
        email: email.from,
        source: 'email',
        initial_message: email.body,
        subject: email.subject
      });
    }
    
    return customer;
  }

  private async processAttachments(attachments: any[]) {
    const processedAttachments = [];
    
    for (const attachment of attachments) {
      try {
        // Upload to Supabase Storage
        const { data: uploadData } = await supabase.storage
          .from('email-attachments')
          .upload(`${Date.now()}-${attachment.filename}`, attachment.content);

        // Extract text from images/PDFs
        let extractedText = '';
        if (attachment.content_type.startsWith('image/')) {
          extractedText = await this.extractTextFromImage(attachment);
        } else if (attachment.content_type === 'application/pdf') {
          extractedText = await this.extractTextFromPDF(attachment);
        }

        // Analyze attachment type
        const isInvoice = this.isInvoiceAttachment(attachment.filename, extractedText);
        const isPhoto = attachment.content_type.startsWith('image/');

        processedAttachments.push({
          filename: attachment.filename,
          content_type: attachment.content_type,
          size: attachment.size,
          url: uploadData?.path,
          extracted_text: extractedText,
          is_invoice: isInvoice,
          is_photo: isPhoto
        });
      } catch (error) {
        console.error('Attachment processing error:', error);
      }
    }
    
    return processedAttachments;
  }

  private async triggerAutomatedResponse(email: InboundEmail, customer: Customer, analysis: any) {
    // Don't auto-respond to auto-responses
    if (this.isAutoResponse(email)) return;
    
    const responseTemplate = this.selectResponseTemplate(analysis);
    
    if (responseTemplate) {
      const personalizedResponse = await this.personalizeResponse(
        responseTemplate, 
        customer, 
        analysis
      );
      
      await this.sendAutomatedResponse(email, personalizedResponse);
      
      // Mark as auto-responded
      await supabase
        .from('communications')
        .update({ auto_response_sent: true })
        .eq('id', email.id);
    }
  }

  private selectResponseTemplate(analysis: any): string | null {
    switch (analysis.intent) {
      case 'emergency':
        return 'emergency_response';
      case 'quote_request':
        return 'quote_request_response';
      case 'scheduling':
        return 'scheduling_response';
      case 'complaint':
        return analysis.sentiment < -0.5 ? 'complaint_urgent' : 'complaint_standard';
      case 'inquiry':
        return 'general_inquiry';
      default:
        return 'acknowledgment';
    }
  }

  private async personalizeResponse(template: string, customer: Customer, analysis: any) {
    const templates = {
      emergency_response: `
        Hi ${customer.first_name},
        
        We received your emergency HVAC request and understand the urgency.
        
        🚨 EMERGENCY RESPONSE ACTIVATED
        
        A technician will contact you within 30 minutes.
        Emergency hotline: ${process.env.EMERGENCY_PHONE}
        
        Issue detected: ${analysis.key_details?.problem_description || 'HVAC emergency'}
        
        We're on our way!
        ${process.env.COMPANY_NAME} Emergency Team
      `,
      
      quote_request_response: `
        Hi ${customer.first_name},
        
        Thank you for your interest in our HVAC services!
        
        📋 QUOTE REQUEST RECEIVED
        
        Service needed: ${analysis.key_details?.equipment_type || 'HVAC service'}
        
        What happens next:
        1. Our estimator will review your request (within 2 hours)
        2. We'll schedule a convenient time for assessment
        3. You'll receive a detailed quote within 24 hours
        
        Questions? Reply to this email or call ${process.env.COMPANY_PHONE}
        
        Best regards,
        ${process.env.COMPANY_NAME} Team
      `,
      
      complaint_urgent: `
        Hi ${customer.first_name},
        
        We sincerely apologize for the issue you've experienced.
        
        🔧 PRIORITY RESOLUTION
        
        Your concern is our top priority. A manager will contact you within 1 hour to resolve this matter.
        
        Reference: ${analysis.key_details?.reference || 'N/A'}
        
        We value your business and will make this right.
        
        ${process.env.COMPANY_NAME} Management Team
      `
    };
    
    return templates[template] || templates.acknowledgment;
  }
}
```

### 🎤 **Voice Transcription System**

```typescript
// src/services/transcription.service.ts
import { OpenAI } from 'openai';
import { supabase } from './supabase';

export class TranscriptionService {
  private openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

  async transcribeAudio(audioFile: File, context: 'customer_call' | 'technician_note' | 'voicemail') {
    try {
      // 1. Upload audio to Supabase Storage
      const fileName = `${Date.now()}-${audioFile.name}`;
      const { data: uploadData } = await supabase.storage
        .from('audio-recordings')
        .upload(fileName, audioFile);

      // 2. Transcribe with OpenAI Whisper
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        language: 'pl', // Polish support
        prompt: this.getContextPrompt(context),
        response_format: 'verbose_json',
        temperature: 0.1
      });

      // 3. Analyze transcription
      const analysis = await this.analyzeTranscription(transcription.text, context);

      // 4. Store transcription
      const storedTranscription = await supabase
        .from('transcriptions')
        .insert({
          audio_url: uploadData?.path,
          transcript: transcription.text,
          context,
          confidence: transcription.segments?.reduce((acc, seg) => acc + seg.avg_logprob, 0) / transcription.segments?.length,
          duration: transcription.duration,
          language: transcription.language,
          analysis,
          created_at: new Date()
        })
        .select()
        .single();

      // 5. Process based on context
      await this.processTranscriptionByContext(storedTranscription, analysis);

      return storedTranscription;
    } catch (error) {
      console.error('Transcription error:', error);
      throw error;
    }
  }

  private getContextPrompt(context: string): string {
    const prompts = {
      customer_call: 'This is a customer service call for an HVAC company. Focus on technical terms, equipment names, and service requests.',
      technician_note: 'This is a technician recording notes about HVAC equipment, repairs, or maintenance. Focus on technical details and part numbers.',
      voicemail: 'This is a voicemail message for an HVAC company. Focus on contact information and service requests.'
    };
    
    return prompts[context] || '';
  }

  private async analyzeTranscription(text: string, context: string) {
    const prompt = `
      Analyze this HVAC ${context} transcription and extract:
      
      Transcription: "${text}"
      
      Extract:
      1. Intent (service_request, emergency, complaint, inquiry, scheduling)
      2. Equipment mentioned (furnace, AC, heat pump, etc.)
      3. Problem description
      4. Urgency level (low, normal, high, urgent)
      5. Customer sentiment (-1 to 1)
      6. Contact information mentioned
      7. Preferred dates/times mentioned
      8. Technical details (part numbers, error codes, symptoms)
      9. Action items
      10. Follow-up required (yes/no)
      
      Return as JSON.
    `;

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    });

    return JSON.parse(response.choices[0].message.content);
  }

  private async processTranscriptionByContext(transcription: any, analysis: any) {
    switch (transcription.context) {
      case 'customer_call':
        await this.processCustomerCall(transcription, analysis);
        break;
      case 'technician_note':
        await this.processTechnicianNote(transcription, analysis);
        break;
      case 'voicemail':
        await this.processVoicemail(transcription, analysis);
        break;
    }
  }

  private async processCustomerCall(transcription: any, analysis: any) {
    // Create communication record
    await supabase.from('communications').insert({
      type: 'phone',
      direction: 'inbound',
      content: transcription.transcript,
      transcription_id: transcription.id,
      sentiment_score: analysis.sentiment,
      created_at: new Date()
    });

    // Create service ticket if needed
    if (analysis.intent === 'service_request' || analysis.intent === 'emergency') {
      await this.createServiceTicketFromCall(transcription, analysis);
    }

    // Send follow-up if required
    if (analysis.follow_up_required) {
      await this.scheduleFollowUp(transcription, analysis);
    }
  }

  private async processTechnicianNote(transcription: any, analysis: any) {
    // Add to service ticket notes
    if (analysis.service_ticket_id) {
      await supabase
        .from('service_tickets')
        .update({
          notes: supabase.raw(`notes || '\n\nTechnician Note: ${transcription.transcript}'`),
          updated_at: new Date()
        })
        .eq('id', analysis.service_ticket_id);
    }

    // Update equipment records if technical details found
    if (analysis.technical_details) {
      await this.updateEquipmentFromNotes(analysis);
    }
  }

  async transcribePhoneCall(callSid: string, recordingUrl: string) {
    try {
      // Download recording from Twilio
      const audioResponse = await fetch(recordingUrl);
      const audioBuffer = await audioResponse.arrayBuffer();
      const audioFile = new File([audioBuffer], `call-${callSid}.wav`, { type: 'audio/wav' });

      // Transcribe
      const transcription = await this.transcribeAudio(audioFile, 'customer_call');

      // Link to call record
      await supabase
        .from('phone_calls')
        .update({ transcription_id: transcription.id })
        .eq('call_sid', callSid);

      return transcription;
    } catch (error) {
      console.error('Phone call transcription error:', error);
      throw error;
    }
  }
}
```

### 📞 **Real-Time Call Transcription Component**

```typescript
// src/components/hvac/communications/RealTimeTranscription.tsx
import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MicrophoneIcon, StopIcon, PlayIcon } from '@heroicons/react/24/outline';
import { useTranscription } from '../../../hooks/useTranscription';

interface RealTimeTranscriptionProps {
  onTranscriptionComplete: (transcription: any) => void;
  context: 'customer_call' | 'technician_note' | 'voicemail';
  customerId?: string;
  serviceTicketId?: string;
}

const RealTimeTranscription: React.FC<RealTimeTranscriptionProps> = ({
  onTranscriptionComplete,
  context,
  customerId,
  serviceTicketId
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [duration, setDuration] = useState(0);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  const { transcribeAudio } = useTranscription();

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        }
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processRecording(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);
      setDuration(0);

      // Update duration
      intervalRef.current = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsProcessing(true);
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  const processRecording = async (audioBlob: Blob) => {
    try {
      const audioFile = new File([audioBlob], `recording-${Date.now()}.webm`, {
        type: 'audio/webm'
      });

      const result = await transcribeAudio(audioFile, context);
      
      setTranscript(result.transcript);
      setConfidence(result.confidence);
      setIsProcessing(false);
      
      onTranscriptionComplete({
        ...result,
        customer_id: customerId,
        service_ticket_id: serviceTicketId
      });

    } catch (error) {
      console.error('Error processing recording:', error);
      setIsProcessing(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="real-time-transcription bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">
          {context === 'customer_call' && '📞 Customer Call'}
          {context === 'technician_note' && '🔧 Technician Note'}
          {context === 'voicemail' && '📧 Voicemail'}
        </h3>
        
        <div className="text-sm text-gray-600">
          Duration: {formatDuration(duration)}
        </div>
      </div>

      {/* Recording Controls */}
      <div className="flex justify-center mb-6">
        <AnimatePresence>
          {!isRecording && !isProcessing && (
            <motion.button
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              onClick={startRecording}
              className="w-20 h-20 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"
            >
              <MicrophoneIcon className="w-8 h-8" />
            </motion.button>
          )}

          {isRecording && (
            <motion.button
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              onClick={stopRecording}
              className="w-20 h-20 bg-gray-800 hover:bg-gray-900 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"
            >
              <StopIcon className="w-8 h-8" />
            </motion.button>
          )}

          {isProcessing && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="w-20 h-20 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-lg"
            >
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Recording Status */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-center mb-4"
          >
            <div className="flex items-center justify-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-red-600 font-medium">Recording...</span>
            </div>
          </motion.div>
        )}

        {isProcessing && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-center mb-4"
          >
            <div className="text-blue-600 font-medium">Processing transcription...</div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Transcription Result */}
      <AnimatePresence>
        {transcript && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-50 rounded-lg p-4"
          >
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Transcription</span>
              <span className="text-sm text-gray-500">
                Confidence: {Math.round(confidence * 100)}%
              </span>
            </div>
            <p className="text-gray-800 leading-relaxed">{transcript}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Context-Specific Actions */}
      {transcript && (
        <div className="mt-4 flex space-x-2">
          <button className="hvac-button hvac-button--primary">
            Create Service Ticket
          </button>
          <button className="hvac-button hvac-button--outline">
            Send Summary
          </button>
          <button className="hvac-button hvac-button--outline">
            Schedule Follow-up
          </button>
        </div>
      )}
    </div>
  );
};

export default RealTimeTranscription;
```

### 📊 **Communication Analytics Dashboard**

```typescript
// src/components/hvac/analytics/CommunicationAnalytics.tsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  LineChart, Line, AreaChart, Area, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer 
} from 'recharts';

const CommunicationAnalytics: React.FC = () => {
  const [emailMetrics, setEmailMetrics] = useState({
    totalEmails: 0,
    responseRate: 0,
    averageResponseTime: 0,
    sentimentScore: 0
  });

  const [transcriptionMetrics, setTranscriptionMetrics] = useState({
    totalTranscriptions: 0,
    averageAccuracy: 0,
    processingTime: 0,
    languageDistribution: []
  });

  return (
    <div className="communication-analytics p-6">
      <h1 className="text-3xl font-bold mb-6">Communication Analytics</h1>

      {/* Email Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-blue-600">
            {emailMetrics.totalEmails}
          </div>
          <div className="text-sm text-gray-600">Total Emails</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-green-600">
            {emailMetrics.responseRate}%
          </div>
          <div className="text-sm text-gray-600">Response Rate</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-orange-600">
            {emailMetrics.averageResponseTime}h
          </div>
          <div className="text-sm text-gray-600">Avg Response Time</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-purple-600">
            {emailMetrics.sentimentScore}/5
          </div>
          <div className="text-sm text-gray-600">Sentiment Score</div>
        </motion.div>
      </div>

      {/* Transcription Quality */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="hvac-card mb-8"
      >
        <h2 className="text-xl font-semibold mb-4">Transcription Quality</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              {transcriptionMetrics.averageAccuracy}%
            </div>
            <div className="text-sm text-gray-600">Average Accuracy</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">
              {transcriptionMetrics.processingTime}s
            </div>
            <div className="text-sm text-gray-600">Processing Time</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">
              {transcriptionMetrics.totalTranscriptions}
            </div>
            <div className="text-sm text-gray-600">Total Transcriptions</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default CommunicationAnalytics;
```

This comprehensive email and transcription system provides intelligent email processing, real-time voice transcription, automated responses, and detailed analytics - all with the cosmic power your HVAC business needs for complete communication management!
